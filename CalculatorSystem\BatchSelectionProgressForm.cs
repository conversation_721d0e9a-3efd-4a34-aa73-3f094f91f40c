using System;
using System.Drawing;
using System.Windows.Forms;

namespace CalculatorSystem
{
    /// <summary>
    /// 批量选择操作进度弹窗
    /// </summary>
    public partial class BatchSelectionProgressForm : Form
    {
        private ProgressBar progressBar;
        private Label messageLabel;
        private Label progressLabel;
        private Button cancelButton;
        private int totalItems;
        private string operationType;

        /// <summary>
        /// 操作取消事件
        /// </summary>
        public event Action OperationCancelled;

        public BatchSelectionProgressForm(string operationType, int totalItems, Form owner)
        {
            this.operationType = operationType;
            this.totalItems = totalItems;
            this.Owner = owner;
            
            InitializeComponent();
            InitializeContent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 窗体设置
            this.AutoScaleDimensions = new SizeF(7F, 17F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.BackColor = Color.White;
            this.ClientSize = new Size(450, 160);
            this.ControlBox = false;
            this.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "BatchSelectionProgressForm";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "批量操作进度";
            this.TopMost = true;

            // 消息标签
            this.messageLabel = new Label();
            this.messageLabel.AutoSize = false;
            this.messageLabel.Font = new Font("微软雅黑", 10F, FontStyle.Regular, GraphicsUnit.Point, 134);
            this.messageLabel.ForeColor = Color.FromArgb(64, 64, 64);
            this.messageLabel.Location = new Point(30, 25);
            this.messageLabel.Size = new Size(390, 25);
            this.messageLabel.TextAlign = ContentAlignment.MiddleLeft;

            // 进度标签
            this.progressLabel = new Label();
            this.progressLabel.AutoSize = false;
            this.progressLabel.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            this.progressLabel.ForeColor = Color.FromArgb(100, 100, 100);
            this.progressLabel.Location = new Point(30, 55);
            this.progressLabel.Size = new Size(390, 20);
            this.progressLabel.TextAlign = ContentAlignment.MiddleLeft;

            // 进度条
            this.progressBar = new ProgressBar();
            this.progressBar.Location = new Point(30, 80);
            this.progressBar.Size = new Size(390, 23);
            this.progressBar.Style = ProgressBarStyle.Continuous;
            this.progressBar.Minimum = 0;
            this.progressBar.Maximum = 100;
            this.progressBar.Value = 0;

            // 取消按钮
            this.cancelButton = new Button();
            this.cancelButton.BackColor = Color.FromArgb(231, 76, 60);
            this.cancelButton.FlatAppearance.BorderSize = 0;
            this.cancelButton.FlatStyle = FlatStyle.Flat;
            this.cancelButton.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            this.cancelButton.ForeColor = Color.White;
            this.cancelButton.Location = new Point(185, 115);
            this.cancelButton.Size = new Size(80, 30);
            this.cancelButton.Text = "取消";
            this.cancelButton.UseVisualStyleBackColor = false;
            this.cancelButton.Click += CancelButton_Click;

            // 添加控件
            this.Controls.Add(this.messageLabel);
            this.Controls.Add(this.progressLabel);
            this.Controls.Add(this.progressBar);
            this.Controls.Add(this.cancelButton);

            this.ResumeLayout(false);
        }

        private void InitializeContent()
        {
            this.messageLabel.Text = $"正在{operationType} {totalItems:N0} 个组合项目";
            this.progressLabel.Text = "准备开始...";
            this.progressBar.Maximum = totalItems;
        }

        /// <summary>
        /// 更新进度
        /// </summary>
        /// <param name="current">当前进度</param>
        /// <param name="total">总数</param>
        /// <param name="message">自定义消息</param>
        public void UpdateProgress(int current, int total, string message = null)
        {
            if (InvokeRequired)
            {
                BeginInvoke(new Action<int, int, string>(UpdateProgress), current, total, message);
                return;
            }

            if (IsDisposed || !IsHandleCreated)
                return;

            try
            {
                progressBar.Value = Math.Min(current, Math.Max(total, 1));

                if (!string.IsNullOrEmpty(message))
                {
                    progressLabel.Text = message;
                }
                else
                {
                    double percentage = total > 0 ? (double)current / total * 100 : 0;
                    progressLabel.Text = $"进度: {current:N0}/{total:N0} ({percentage:F1}%)";
                }

                // 强制刷新界面
                progressLabel.Refresh();
                progressBar.Refresh();
                Application.DoEvents();
            }
            catch (Exception)
            {
                // 忽略在窗口关闭过程中可能出现的异常
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            cancelButton.Enabled = false;
            cancelButton.Text = "取消中...";
            cancelButton.BackColor = Color.FromArgb(149, 165, 166);
            
            OperationCancelled?.Invoke();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // 确保在窗口关闭时触发取消事件
            if (cancelButton.Enabled)
            {
                OperationCancelled?.Invoke();
            }
            base.OnFormClosing(e);
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            // 确保窗口在父窗口中央显示
            if (Owner != null)
            {
                Location = new Point(
                    Owner.Location.X + (Owner.Width - Width) / 2,
                    Owner.Location.Y + (Owner.Height - Height) / 2
                );
            }
        }
    }
}
